/** @type {import('next').NextConfig} */
const nextConfig = {
  env: {
    API_BASE_URL: 'https://api.exchangim.com/api/',
    API_BASE_URL_STORAGE: 'https://api.exchangim.com/',
  },
  images: {
    domains: ['coin-images.coingecko.com', 'assets.coingecko.com','api.exchangim.com','api.qrserver.com'],
  },
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },
};

const path = require('path');
nextConfig.webpack = (config) => {
  config.resolve = config.resolve || {};
  config.resolve.alias = config.resolve.alias || {};
  config.resolve.alias['next/link'] = path.resolve(__dirname, 'src/components/AutoLink.tsx');
  return config;
};

module.exports = nextConfig;
