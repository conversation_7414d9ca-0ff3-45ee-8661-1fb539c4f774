import forge from 'node-forge';

// RSA Public Key for encryption
export const RSA_PUBLIC_KEY = `-----B<PERSON>IN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArklssikVb+KmZV7gEgCL
Ht7TTChjAiIg6rjUjubAaZH5TLyG9pNJGdoeyV6pMG8WUZ3SLqrzzamXIF7mYsxq
SbbZmYn4wHCqNgpG9aejo/p5AZjTSZtWvtnrvgHOnneQCzhMD5tqlu4lDdCJLoi1
0DEWK/LMyqRU4yLeYPBHjJHN4EsnYEiQrYNZwQ7o9kBAHBMVg76+IGoZsjqYGwNm
uoOJR23jjcrsrES7q9LvXor/feTkvbnL3k6KvVgWBmL7x+w1m90gvygplYwSNWf9
BuPxqI0naDt0dUxuY2GN7dppTGE2RZzafvyRTRNQyPxnVU3XWuJw3x+pIEwRe6b2
swIDAQAB
-----END PUBLIC KEY-----`;

/**
 * Hybrid encryption: encrypts data with AES, then encrypts AES key with RSA
 * @param data - The data to encrypt (object)
 * @param publicKeyPem - The RSA public key in PEM format
 * @returns { key, data, iv } all base64 encoded
 */
export function hybridEncrypt(data: any, publicKeyPem: string = RSA_PUBLIC_KEY) {
  // 1. Generate random AES key and IV
  const key = forge.random.getBytesSync(32); // 256bit
  const iv = forge.random.getBytesSync(16);  // 128bit

  // 2. Encrypt data with AES
  const cipher = forge.cipher.createCipher('AES-CBC', key);
  cipher.start({ iv });
  cipher.update(forge.util.createBuffer(JSON.stringify(data), 'utf8'));
  cipher.finish();
  const encryptedData = cipher.output.getBytes();

  // 3. Encrypt AES key with RSA
  const publicKey = forge.pki.publicKeyFromPem(publicKeyPem);
  const encryptedKey = publicKey.encrypt(key, 'RSA-OAEP');

  // 4. Return base64 encoded values
  return {
    key: forge.util.encode64(encryptedKey),
    data: forge.util.encode64(encryptedData),
    iv: forge.util.encode64(iv)
  };
}

/**
 * Checks if the data should be encrypted
 * @param data - The request data
 * @returns boolean indicating if encryption is needed
 */
export function shouldEncryptData(data: any): boolean {
  // Skip encryption for FormData (file uploads) or if data is null/undefined
  if (!data || data instanceof FormData) {
    return false;
  }
  
  // Skip encryption if data is already encrypted (has 'data' property with string value)
  if (typeof data === 'object' && 'data' in data && typeof data.data === 'string') {
    return false;
  }
  
  return true;
}
