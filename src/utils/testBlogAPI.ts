// Test file for blog API endpoints

export async function testBlogAPI() {
  console.log('🧪 Testing Blog API endpoints...');
  
  const baseUrl = 'https://api.exchangim.com/api';
  
  try {
    // Test blog posts endpoint
    console.log('📝 Testing blog posts endpoint...');
    const postsResponse = await fetch(`${baseUrl}/blog/posts`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });
    
    console.log('Posts Response Status:', postsResponse.status);
    
    if (postsResponse.ok) {
      const postsData = await postsResponse.json();
      console.log('✅ Blog posts fetched successfully:', postsData);
    } else {
      console.log('❌ Blog posts failed:', postsResponse.statusText);
      const errorText = await postsResponse.text();
      console.log('Error details:', errorText);
    }
    
    // Test blog categories endpoint
    console.log('📂 Testing blog categories endpoint...');
    const categoriesResponse = await fetch(`${baseUrl}/blog/categories`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });
    
    console.log('Categories Response Status:', categoriesResponse.status);
    
    if (categoriesResponse.ok) {
      const categoriesData = await categoriesResponse.json();
      console.log('✅ Blog categories fetched successfully:', categoriesData);
    } else {
      console.log('❌ Blog categories failed:', categoriesResponse.statusText);
      const errorText = await categoriesResponse.text();
      console.log('Error details:', errorText);
    }
    
  } catch (error) {
    console.error('🚨 Error testing blog API:', error);
  }
}

// Function to test with different page numbers
export async function testBlogPagination() {
  console.log('📄 Testing blog pagination...');
  
  const baseUrl = 'https://api.exchangim.com/api';
  
  for (let page = 1; page <= 3; page++) {
    try {
      console.log(`Testing page ${page}...`);
      const response = await fetch(`${baseUrl}/blog/posts?page=${page}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ Page ${page}:`, {
          current_page: data.result?.current_page,
          total: data.result?.total,
          per_page: data.result?.per_page,
          last_page: data.result?.last_page,
          posts_count: data.result?.data?.length
        });
      } else {
        console.log(`❌ Page ${page} failed:`, response.status);
        break;
      }
    } catch (error) {
      console.error(`🚨 Error testing page ${page}:`, error);
      break;
    }
  }
}

// Function to test individual post
export async function testBlogPost(postId: number) {
  console.log(`📖 Testing individual blog post ${postId}...`);
  
  const baseUrl = 'https://api.exchangim.com/api';
  
  try {
    const response = await fetch(`${baseUrl}/blog/posts/${postId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });
    
    console.log(`Post ${postId} Response Status:`, response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Blog post ${postId} fetched successfully:`, data);
    } else {
      console.log(`❌ Blog post ${postId} failed:`, response.statusText);
      const errorText = await response.text();
      console.log('Error details:', errorText);
    }
  } catch (error) {
    console.error(`🚨 Error testing blog post ${postId}:`, error);
  }
}

// Run all tests
export async function runAllBlogTests() {
  console.log('🚀 Running all blog API tests...');
  
  await testBlogAPI();
  console.log('\n' + '='.repeat(50) + '\n');
  
  await testBlogPagination();
  console.log('\n' + '='.repeat(50) + '\n');
  
  await testBlogPost(1);
  
  console.log('✨ All tests completed!');
}

// Make functions available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testBlogAPI = testBlogAPI;
  (window as any).testBlogPagination = testBlogPagination;
  (window as any).testBlogPost = testBlogPost;
  (window as any).runAllBlogTests = runAllBlogTests;
}
