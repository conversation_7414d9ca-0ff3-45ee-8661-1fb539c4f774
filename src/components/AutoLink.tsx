// src/components/AutoLink.tsx
'use client';
// @ts-ignore
import Link from '../../node_modules/next/link';
import type { LinkProps } from 'next/link';
import { ReactNode } from 'react';

export default function AutoLink({ children, ...props }: LinkProps & { children: ReactNode }) {
  return (
    <Link
      {...props}
      onClick={e => {
        if (typeof window !== 'undefined') {
          const NProgressModule = require('nprogress-v2');
          const NProgress = NProgressModule?.NProgress;
          if (NProgress) NProgress.start();
        }
        if (props.onClick) props.onClick(e);
      }}
    >
      {children}
    </Link>
  );
} 