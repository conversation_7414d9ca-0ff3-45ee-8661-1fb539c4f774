// src/components/ProgressLink.tsx
'use client';
import Link, { LinkProps } from 'next/link';
import { ReactNode } from 'react';

export default function ProgressLink({ children, ...props }: LinkProps & { children: ReactNode }) {
  return (
    <Link
      {...props}
      onClick={e => {
        if (typeof window !== 'undefined') {
          const NProgressModule = require('nprogress-v2');
          const NProgress = NProgressModule?.NProgress;
          if (NProgress) NProgress.start();
        }
        if (props.onClick) props.onClick(e);
      }}
    >
      {children}
    </Link>
  );
} 