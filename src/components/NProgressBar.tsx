// src/components/NProgressBar.tsx
'use client';
import { usePathname, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import 'nprogress-v2/dist/index.css';

export default function NProgressBar() {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Use require to avoid ESM/CommonJS interop issues
    const NProgress = require('nprogress-v2').NProgress;
    NProgress.configure({ showSpinner: true, direction: 'rtl' });
    NProgress.done();
  }, [pathname, searchParams]);

  return null;
} 