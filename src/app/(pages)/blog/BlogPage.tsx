'use client';

import Footer from "../components/Footer";
import Navbar from "../components/Navbar";
import { FaCalendarAlt, FaHeart, FaCommentDots, FaShareAlt, FaEye, FaSpinner } from "react-icons/fa";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import BlogSeo from "./components/BlogSeo";
import BlogSeoContent from "./components/BlogSeoContent";
import { getBlogPosts, getBlogCategories, type BlogPost, type BlogCategory } from "@/requests/blogRequest";
import { formatPersianDate, getBlogImageUrl } from "@/utils/blogHelpers";

const getCommentsCount = (blogId: number) => {
  if (typeof window !== 'undefined') {
    const comments = JSON.parse(localStorage.getItem(`comments-${blogId}`) || '[]');
    return comments.length;
  }
  return 0;
}

interface BlogCard {
  id: number;
  title: string;
  date: string;
  image: string;
  description: string;
  category: string;
  views: number;
  likes: number;
  comments: number;
}

export default function BlogPage() {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1);
  const [showAll, setShowAll] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [shareUrl, setShareUrl] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>("");

  // API data states
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [categories, setCategories] = useState<BlogCategory[]>([]);
  const [totalPages, setTotalPages] = useState(1);
  const [totalPosts, setTotalPosts] = useState(0);

  // Legacy local storage data for likes/views
  const [cardsData, setCardsData] = useState<BlogCard[]>([]);

  // Fetch blog posts and categories from API
  useEffect(() => {
    const fetchBlogData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch categories
        const categoriesResult = await getBlogCategories();
        if (!categoriesResult.isError && categoriesResult.data) {
          setCategories(categoriesResult.data);
        }

        // Fetch blog posts
        const postsResult = await getBlogPosts(currentPage, selectedCategory);
        if (!postsResult.isError && postsResult.data) {
          setBlogPosts(postsResult.data.data);
          setTotalPages(postsResult.data.last_page);
          setTotalPosts(postsResult.data.total);

          // Convert API data to legacy format for local storage compatibility
          const legacyCards: BlogCard[] = postsResult.data.data.map(post => ({
            id: post.id,
            title: post.title,
            date: formatPersianDate(post.published_at),
            image: getBlogImageUrl(post.cover),
            description: post.excerpt,
            category: `#${post.category.name}`,
            views: 0, // Will be loaded from localStorage
            likes: 0, // Will be loaded from localStorage
            comments: getCommentsCount(post.id)
          }));

          // Load existing data from localStorage and merge
          if (typeof window !== 'undefined') {
            const savedData = localStorage.getItem('cardsData');
            if (savedData) {
              const existingData: BlogCard[] = JSON.parse(savedData);
              const mergedData = legacyCards.map(newCard => {
                const existing = existingData.find(card => card.id === newCard.id);
                return existing ? { ...newCard, views: existing.views, likes: existing.likes } : newCard;
              });
              setCardsData(mergedData);
            } else {
              setCardsData(legacyCards);
            }
          }
        } else {
          setError(postsResult.message || 'خطا در دریافت مقالات');
        }
      } catch (err) {
        setError('خطا در اتصال به سرور');
        console.error('Error fetching blog data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchBlogData();
  }, [currentPage, selectedCategory]);

  const saveToLocalStorage = (newData: BlogCard[]) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('cardsData', JSON.stringify(newData));
      setCardsData(newData);
    }
  };

  const handleShare = (e: React.MouseEvent, id: number) => {
    e.stopPropagation();
    if (typeof window !== 'undefined') {
      setShareUrl(`${window.location.origin}/blog/${id}`);
      setShowShareModal(true);
    }
  };

  const handleLike = (e: React.MouseEvent, id: number) => {
    e.stopPropagation();
    const newData = cardsData.map(card =>
      card.id === id ? { ...card, likes: card.likes + 1 } : card
    );
    saveToLocalStorage(newData);
  };

  const handleView = (id: number) => {
    const newData = cardsData.map(card =>
      card.id === id ? { ...card, views: card.views + 1 } : card
    );
    saveToLocalStorage(newData);
  };

  const handleCardClick = (id: number) => {
    handleView(id);
    router.push(`/blog/${id}`);
  };

  const filteredCards = cardsData;
  const cardsPerPage = 6;
  const legacyTotalPages = Math.ceil(filteredCards.length / cardsPerPage);
  const currentCards = showAll ? filteredCards : filteredCards.slice((currentPage - 1) * cardsPerPage, currentPage * cardsPerPage);

  return (
    <div className="bg-[#18191D] text-white min-h-screen">
      {/* SEO Structured Data */}
      <BlogSeo />

      <div className="flex justify-center w-full bg-transparent fixed top-0 left-0 right-0 z-50">
        <div className="w-full max-w-[1100px]">
          <Navbar
            className="py-4 border-b border-gray-800 backdrop-blur-sm bg-transparent"
            hideSearch
            hideSignUp
          />
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        <Swiper
          spaceBetween={30}
          slidesPerView={1}
          className="w-full lg:w-[1100px] mt-20"
        >
          <SwiperSlide>
            <img src="/images/bitcoin.png" alt="آخرین اخبار بیت کوین" className="w-full h-[250px] md:h-[400px] object-cover rounded" />
          </SwiperSlide>
          <SwiperSlide>
            <img src="/images/bitcoin.png" alt="تحلیل قیمت ارزهای دیجیتال" className="w-full h-[250px] md:h-[400px] object-cover rounded" />
          </SwiperSlide>
          <SwiperSlide>
            <img src="/images/bitcoin.png" alt="آموزش ارزهای دیجیتال" className="w-full h-[250px] md:h-[400px] object-cover rounded" />
          </SwiperSlide>
        </Swiper>
      </div>

      <div className="container mx-auto px-4 py-10 flex flex-col items-center">
        <div className="flex justify-between w-full lg:w-[1100px] items-center">
          <h1 className="text-3xl font-bold text-left">مقالات اخیر</h1>
          <div className="flex gap-4 items-center">
            {/* Category Filter */}
            {categories.length > 0 && (
              <select
                value={selectedCategory}
                onChange={(e) => {
                  setSelectedCategory(e.target.value);
                  setCurrentPage(1);
                }}
                className="bg-gray-800 text-white rounded-lg py-2 px-4 text-sm border border-gray-600"
              >
                <option value="">همه دسته‌بندی‌ها</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.slug}>
                    {category.name}
                  </option>
                ))}
              </select>
            )}
            <button onClick={() => setShowAll(!showAll)} className="text-right text-black bg-white rounded-lg py-2 px-4 text-sm">
              {showAll ? "مقالات صفحه‌بندی شده" : "همه مقالات"}
            </button>
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-20">
            <FaSpinner className="animate-spin text-4xl text-white" />
            <span className="mr-4 text-lg">در حال بارگذاری...</span>
          </div>
        )}

        {/* Error State with Fallback */}
        {error && !loading && (
          <div className="flex justify-center items-center py-20">
            <div className="text-center">
              <p className="text-red-500 text-lg mb-4">{error}</p>
              <div className="flex gap-4 justify-center">
                <button
                  onClick={() => window.location.reload()}
                  className="bg-white text-black px-6 py-2 rounded-lg hover:bg-gray-200"
                >
                  تلاش مجدد
                </button>
                <button
                  onClick={() => {
                    setError(null);
                    setLoading(false);
                    // Load demo data as fallback
                    const demoCards: BlogCard[] = [
                      { id: 1, title: "آخرین اخبار بیت کوین", date: "۱۴۰۳/۰۵/۰۱", image: "/images/blog1.png", description: "قیمت بیت کوین در بالاترین سطح خود معامله می‌شود", category: "#اخبار", views: 0, likes: 0, comments: 0 },
                      { id: 2, title: "راهنمای خرید ارز دیجیتال", date: "۱۴۰۳/۰۴/۲۸", image: "/images/blog2.png", description: "آموزش کامل خرید و فروش ارزهای دیجیتال", category: "#آموزش", views: 0, likes: 0, comments: 0 },
                      { id: 3, title: "تحلیل بازار ارزهای دیجیتال", date: "۱۴۰۳/۰۴/۲۵", image: "/images/blog3.png", description: "بررسی روند قیمت‌ها و پیش‌بینی آینده", category: "#تحلیل", views: 0, likes: 0, comments: 0 },
                    ];
                    setCardsData(demoCards);
                  }}
                  className="bg-gray-700 text-white px-6 py-2 rounded-lg hover:bg-gray-600"
                >
                  نمایش محتوای نمونه
                </button>
              </div>
            </div>
          </div>
        )}

        {/* No Posts State */}
        {!loading && !error && currentCards.length === 0 && (
          <div className="flex justify-center items-center py-20">
            <p className="text-gray-400 text-lg">هیچ مقاله‌ای یافت نشد</p>
          </div>
        )}

        {/* Blog Posts Grid - Only show when not loading and no error */}
        {!loading && !error && currentCards.length > 0 && (
          <div className="mt-6 grid grid-cols-1 place-items-center lg:grid-cols-3 gap-4 justify-center items-center w-full lg:w-[1100px]">
            {currentCards.map((card) => (
            <div
              key={card.id}
              onClick={() => handleCardClick(card.id)}
              className="rounded-lg shadow-lg relative cursor-pointer"
            >
              <div className="p-0 shadow-lg relative w-full h-[550px] flex flex-col border border-gray-700 rounded-lg hover:transform hover:scale-105 hover:shadow-2xl transition-all duration-300">
                <div
                  className="relative w-full h-[50%] bg-cover bg-center rounded-t-lg cursor-pointer"
                  style={{ backgroundImage: `url(${card.image})` }}
                  aria-label={card.title}
                />

                <div className="flex flex-col justify-between h-[50%] p-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-400 text-sm text-left">
                      <FaCalendarAlt className="mr-2 inline" /> {card.date}
                    </span>
                    <span className="text-white rounded p-1 text-sm">{card.category}</span>
                  </div>

                  <h2 className="text-xl font-semibold mb-2 text-left">{card.title || "Untitled"}</h2>
                  <p className="text-gray-400 mb-4 text-sm text-left">{card.description}</p>

                  <div className="mt-4 flex justify-between items-center text-gray-400">
                    <button
                      className="border border-gray-700 p-2 rounded-lg flex items-center justify-center"
                      onClick={(e) => handleShare(e, card.id)}
                      aria-label="اشتراک گذاری"
                    >
                      <FaShareAlt size={16} />
                    </button>
                    <div className="flex items-center gap-6">
                      <div className="flex items-center border border-gray-700 p-2 rounded-lg">
                        <FaEye size={16} className="ml-3" />
                        <span>{card.views}</span>
                      </div>
                      <div className="flex items-center border border-gray-700 p-2 rounded-lg">
                        <FaHeart
                          size={16}
                          className={`ml-3 cursor-pointer ${card.likes > 0 ? 'text-red-500' : ''}`}
                          onClick={(e) => handleLike(e, card.id)}
                          aria-label="لایک"
                        />
                        <span>{card.likes}</span>
                      </div>
                      <div className="flex items-center border border-gray-700 p-2 rounded-lg">
                        <FaCommentDots size={16} className="ml-3 cursor-pointer" />
                        <span>{getCommentsCount(card.id)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            ))}
          </div>
        )}

        {/* Pagination - Only show when not loading and has posts */}
        {!loading && !error && !showAll && totalPages > 1 && (
          <div className="flex justify-center mt-8">
            <div className="flex items-center gap-2">
              <button
                onClick={() => setCurrentPage(Math.max(currentPage - 1, 1))}
                className="px-4 py-2 text-white bg-gray-700 rounded-lg hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={currentPage === 1 || loading}
                aria-label="صفحه قبلی"
              >
                قبلی
              </button>

              <div className="flex items-center gap-1">
                {/* Show page numbers */}
                {[...Array(Math.min(totalPages, 5))].map((_, index) => {
                  let pageNumber;
                  if (totalPages <= 5) {
                    pageNumber = index + 1;
                  } else if (currentPage <= 3) {
                    pageNumber = index + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNumber = totalPages - 4 + index;
                  } else {
                    pageNumber = currentPage - 2 + index;
                  }

                  return (
                    <button
                      key={pageNumber}
                      onClick={() => setCurrentPage(pageNumber)}
                      className={`px-3 py-2 rounded-lg ${
                        currentPage === pageNumber
                          ? "bg-white text-black"
                          : "bg-gray-700 text-white hover:bg-gray-600"
                      } transition-colors`}
                      aria-label={`صفحه ${pageNumber}`}
                    >
                      {pageNumber}
                    </button>
                  );
                })}
              </div>

              <button
                onClick={() => setCurrentPage(Math.min(currentPage + 1, totalPages))}
                className="px-4 py-2 text-white bg-gray-700 rounded-lg hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={currentPage === totalPages || loading}
                aria-label="صفحه بعدی"
              >
                بعدی
              </button>
            </div>
          </div>
        )}

        {/* Show total posts count */}
        {!loading && !error && totalPosts > 0 && (
          <div className="mt-4 text-center text-gray-400">
            نمایش {currentCards.length} مقاله از {totalPosts} مقاله
          </div>
        )}
      </div>

      {showShareModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-bold mb-4 text-white text-center">اشتراک‌گذاری مقاله</h3>
            <div className="flex gap-2">
              <input
                type="text"
                value={shareUrl}
                readOnly
                className="border p-2 rounded bg-gray-700 text-white"
                aria-label="آدرس مقاله برای اشتراک گذاری"
              />
              <button
                onClick={() => {
                  navigator.clipboard.writeText(shareUrl);
                  setShowShareModal(false);
                }}
                className="bg-white text-black px-4 py-2 rounded hover:bg-blue hover:text-white"
                aria-label="کپی آدرس"
              >
                کپی
              </button>
            </div>
            <button
              onClick={() => setShowShareModal(false)}
              className="mt-4 text-white border border-gray-500 px-4 py-1 rounded hover:text-white"
              aria-label="بستن"
            >
              بستن
            </button>
          </div>
        </div>
      )}

      {/* SEO Content Section - Rich Text for Search Engines */}
      <BlogSeoContent />

      <Footer />
    </div>
  );
}
