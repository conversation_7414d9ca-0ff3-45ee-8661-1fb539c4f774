import { Metadata } from 'next';
import BlogPostPage from './BlogPostPage';
import { apiFetch } from '@/requests/api';

export async function generateMetadata(
  { params }: { params: { id: string } }
): Promise<Metadata> {
  const id = params.id;
  const json = await apiFetch(`blog/posts/${id}`, { next: { revalidate: 60 } });
  if (!json.result) {
    return {
      title: 'مقاله یافت نشد | اکسچنجیم',
      description: 'متاسفانه مقاله مورد نظر یافت نشد.',
    };
  }
  const post = json.result;
  const API_BASE_URL_STORAGE = process.env.API_BASE_URL_STORAGE;
  return {
    title: `${post.title} | وبلاگ اکسچنجیم`,
    description: post.excerpt || post.body?.substring(0, 160),
    openGraph: {
      title: post.title,
      description: post.excerpt || post.body?.substring(0, 160),
      url: `https://exchangim.com/blog/${post.id}`,
      siteName: 'Exchangim',
      images: [
        {
          url: post.cover ? `${API_BASE_URL_STORAGE}/storage/${post.cover}` : '/images/blog1.png',
          width: 1200,
          height: 630,
          alt: post.title,
        },
      ],
      locale: 'fa_IR',
      type: 'article',
      publishedTime: post.published_at,
    },
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: post.excerpt || post.body?.substring(0, 160),
      images: [post.cover ? `${API_BASE_URL_STORAGE}/storage/${post.cover}` : '/images/blog1.png'],
    },
    alternates: {
      canonical: `/blog/${post.id}`,
    },
  };
}

export default async function BlogPost({
  params
}: {
  params: { id: string }
}) {
  const { id } = params;
  return <BlogPostPage id={id} />;
}
