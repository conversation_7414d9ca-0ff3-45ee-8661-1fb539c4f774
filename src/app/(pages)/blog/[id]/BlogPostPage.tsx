'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Navbar from '../../components/Navbar';
import Footer from '../../components/Footer';
import { FaHeart, FaCommentDots, FaShareAlt } from 'react-icons/fa';
import BlogPostSeo from './components/BlogPostSeo';

interface BlogPostPageProps {
  id: string;
}

export default function BlogPostPage({ id }: BlogPostPageProps) {
  const [showComments, setShowComments] = useState(false);
  const [comments, setComments] = useState<string[]>([]);
  const [newComment, setNewComment] = useState('');
  const [loading, setLoading] = useState(true);
  const [post, setPost] = useState<any>(null);
  const [notFound, setNotFound] = useState(false);

  useEffect(() => {
    const fetchPost = async () => {
      setLoading(true);
      setNotFound(false);
      try {
        const res = await fetch(`https://api.exchangim.com/api/blog/posts/${id}`);
        const json = await res.json();
        if (json.status === 'success') {
          setPost(json.result);
        } else {
          setNotFound(true);
        }
      } catch (e) {
        setNotFound(true);
      }
      setLoading(false);
    };
    fetchPost();
  }, [id]);

  useEffect(() => {
    const savedComments = JSON.parse(localStorage.getItem(`comments-${id}`) || '[]');
    setComments(savedComments);
  }, [id]);

  const handleAddComment = () => {
    if (newComment.trim() === '') return;
    const updatedComments = [...comments, newComment];
    setComments(updatedComments);
    localStorage.setItem(`comments-${id}`, JSON.stringify(updatedComments));
    setNewComment('');
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[80vh] bg-gradient-to-br from-[#18191D] via-[#23243a] to-[#1a1a2e]">
        <div className="w-full max-w-3xl animate-pulse rounded-3xl overflow-hidden shadow-2xl border border-gray-800 bg-gradient-to-br from-[#23243a] to-[#18191D] mt-20">
          <div className="h-[320px] w-full bg-gray-800" />
          <div className="p-8">
            <div className="h-8 w-2/3 bg-gray-700 rounded mb-4" />
            <div className="h-4 w-1/3 bg-gray-700 rounded mb-6" />
            <div className="h-4 w-full bg-gray-700 rounded mb-2" />
            <div className="h-4 w-5/6 bg-gray-700 rounded mb-2" />
            <div className="h-4 w-4/6 bg-gray-700 rounded mb-2" />
            <div className="h-4 w-3/6 bg-gray-700 rounded mb-2" />
          </div>
        </div>
      </div>
    );
  }

  if (notFound || !post) {
    return (
      <div className="text-center text-white mt-10 text-xl">
        ❌ مقاله‌ای پیدا نشد!
      </div>
    );
  }

  // Prepare data for BlogPostSeo
  const article = {
    title: post.title,
    date: post.published_at ? new Date(post.published_at).toLocaleDateString('fa-IR') : '',
    image: post.cover ? `https://api.exchangim.com/storage/${post.cover}` : '/images/blog1.png',
    content: post.body,
    category: post.category?.name || '',
    author: post.user?.firstname ? `${post.user.firstname} ${post.user.lastname || ''}` : '',
  };

  return (
    <div className="text-white min-h-screen flex flex-col items-center bg-gradient-to-br from-[#18191D] via-[#23243a] to-[#1a1a2e] font-sans" dir="rtl">
      {/* SEO Structured Data */}
      <BlogPostSeo article={article} />
      <div className="w-full max-w-[1100px]">
        <Navbar
          className="py-4 border-b border-gray-800 backdrop-blur-sm bg-gradient-to-r from-[#23243a]/80 to-[#18191D]/80"
          hideSearch
          hideSignUp
        />
      </div>
      {/* Header with cover image */}
      <div className="w-full flex justify-center">
        <div className="w-full max-w-[1050px] relative mt-10 mx-auto">
          {/* Floating cover image card */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            className="relative z-20 rounded-2xl overflow-hidden shadow-2xl border-2 border-blue-500/30 bg-white/10 backdrop-blur-lg mx-auto"
            style={{ boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.25)' }}
          >
            <div className="relative w-full h-[220px] md:h-[300px] mx-auto">
              <img
                src={article.image}
                alt={article.title}
                className="w-full h-full object-cover object-center rounded-2xl"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-[#18191D]/80 to-transparent rounded-2xl" />
              <div className="absolute right-0 bottom-0 p-6 flex flex-col gap-2 items-end">
                <span className="rounded-full px-4 py-1 text-xs font-bold bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow mb-2 inline-block">{article.category}</span>
                <span className="text-gray-200 text-xs font-bold flex items-center gap-2">
                  <span>{article.date}</span>
                  {article.author && <span className="mx-2">|</span>}
                  {article.author && <span className="text-blue-200">{article.author}</span>}
                </span>
              </div>
            </div>
          </motion.div>
          {/* Main content card */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative z-10 max-w-[1050px] mx-auto -mt-12 md:-mt-16 bg-gradient-to-br from-[#23243a] to-[#18191D] text-white rounded-2xl shadow-xl border-r-8 border-blue-500/60 p-8 pt-16 md:pt-20"
          >
            <h1 className="text-3xl md:text-4xl font-extrabold mb-6 leading-tight text-right tracking-tight">{article.title}</h1>
            <article className="text-lg leading-relaxed mb-8 text-gray-200 whitespace-pre-line text-right">
              {article.content}
            </article>
            <div className="flex flex-wrap gap-4 items-center justify-between mt-8 mb-2 flex-row-reverse">
              {/* <div className="flex items-center gap-4 flex-row-reverse">
                <motion.button
                  whileTap={{ scale: 1.2 }}
                  className="flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-pink-600 to-purple-600 text-white font-bold shadow hover:from-purple-600 hover:to-pink-600 transition"
                >
                  <FaHeart />
                  <span>لایک</span>
                </motion.button>
                <motion.button
                  whileTap={{ scale: 1.1 }}
                  onClick={() => setShowComments(!showComments)}
                  className="flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold shadow hover:from-purple-600 hover:to-blue-600 transition"
                >
                  <FaCommentDots />
                  <span>{comments.length} نظر</span>
                </motion.button>
              </div> */}
              <motion.button
                whileTap={{ scale: 1.1 }}
                className="flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-gray-700 to-gray-900 text-white font-bold shadow hover:from-blue-600 hover:to-purple-600 transition"
                onClick={() => {
                  navigator.clipboard.writeText(window.location.href);
                }}
              >
                <FaShareAlt />
                <span>اشتراک‌گذاری</span>
              </motion.button>
            </div>
          </motion.div>
        </div>
      </div>
      {/* Comments Section */}
      <AnimatePresence>
      {showComments && (
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 40 }}
          transition={{ duration: 0.5 }}
          className="w-full flex justify-center"
        >
          <div className="w-full max-w-[900px] mt-12 mb-16">
            <div className="bg-gradient-to-br from-[#23243a] to-[#18191D] text-white rounded-2xl p-8 shadow-xl border border-blue-200/40">
              <h3 className="text-2xl font-bold mb-6 text-blue-400 text-right">نظرات کاربران</h3>
              <div className="space-y-4 mb-6">
                {comments.length === 0 && <div className="text-gray-400 text-right">نظری ثبت نشده است.</div>}
                {comments.map((comment, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: 40 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="bg-[#1E1F25] p-4 rounded-lg shadow border border-blue-100 text-right"
                  >
                    <p className="text-gray-300">{comment}</p>
                  </motion.div>
                ))}
              </div>
              <div className="mt-6">
                <textarea
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder="نظر خود را بنویسید..."
                  className="w-full p-3 bg-[#1E1F25] rounded-lg text-white border border-blue-200 focus:ring-2 focus:ring-blue-400 focus:outline-none transition text-right"
                  rows={3}
                ></textarea>
                <motion.button
                  whileTap={{ scale: 1.1 }}
                  onClick={handleAddComment}
                  className="mt-4 px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full font-bold shadow hover:from-purple-600 hover:to-blue-600 transition"
                >
                  ارسال نظر
                </motion.button>
              </div>
            </div>
          </div>
        </motion.div>
      )}
      </AnimatePresence>
      <Footer />
    </div>
  );
}
