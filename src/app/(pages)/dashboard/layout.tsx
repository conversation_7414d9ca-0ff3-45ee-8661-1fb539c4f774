import Layout from "@/components/dashboard/layout";
import { Suspense } from "react";
import Link from "next/link";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="bg-[#141416] h-screen overflow-y-auto overflow-x-hidden text-[#FCFCFD] lg:p-6 no-scrollbar">
      <Suspense fallback={<div className="flex items-center justify-center h-screen"><div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div></div>}>
        <Layout>
          {/* Add this link in the navigation/sidebar section */}

          {children}
        </Layout>
      </Suspense>
    </div>
  );
}
