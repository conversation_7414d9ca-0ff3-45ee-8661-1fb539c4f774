"use client";
import {
  getProfile,
  getUserCurrency,
  sellTrade,
  getUserLevelDailyLimits,
} from "@/requests/dashboardRequest";
import { sliceNumber } from "@/lib/helper/sliceNumber";
import Image from "next/image";
import React, { useState, useEffect } from "react";
import toast from "react-hot-toast";
import BtnLoader from "@/components/form/BtnLoader";
import { useRouter, useSearchParams } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";

interface CurrencyItem {
  coin_icon: string;
  coin_type: string;
  coin_price: string;
  toman_sell_price: number;
  toman_buy_price: number;
  balance_toman: number;
  balance: string;
  balance_usd: string;
  name: string;
  id: number;
}

const Sell = () => {
  const [tomanAmount, setTomanAmount] = useState("");
  const [trxAmount, setTrxAmount] = useState("");
  const [selected, setSelected] = useState(0);
  const [info, setInfo] = useState<{
    tomanBalance?: string;
    trxBalance?: string;
    balance?: string;
  }>({});
  const [isLoading, setIsLoading] = useState(true);
  const [active, setActive] = useState(true);
  const [userCurrency, setUserCurrency] = useState<CurrencyItem[]>([]);
  const filtered = userCurrency.find((item) => item.id === selected);
  const [showCardDropdown, setShowCardDropdown] = useState(false);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const id = searchParams.get("id");
  const [limitsLoading, setLimitsLoading] = useState(true);
  const [limitsData, setLimitsData] = useState<{
    buy_limits: {
      daily_limit: number;
      used_today: number;
      remaining_today: number;
      can_buy: boolean;
      limit_reached: boolean;
      formatted_daily_limit: string;
      formatted_used_today: string;
      formatted_remaining_today: string;
    };
    sell_limits: {
      daily_limit: number;
      used_today: number;
      remaining_today: number;
      can_sell: boolean;
      limit_reached: boolean;
      formatted_daily_limit: string;
      formatted_used_today: string;
      formatted_remaining_today: string;
    };
    withdrawal_limits: {
      daily_limit: number;
      formatted_daily_limit: string;
    };
  } | null>(null);

  // Helper function to parse comma-separated numbers
  const parseNumber = (value: string): number => {
    if (!value) return 0;
    // Remove commas and convert to number
    return parseFloat(value.replace(/,/g, '')) || 0;
  };

  async function getProfileHandler() {
    setIsLoading(true);
    try {
      const result = await getProfile();
      if (result.isError) {
        toast.error("خطایی رخ داد");
      } else {
        setInfo(result.data);
      }
    } catch (error) {
      toast.error("خطا در دریافت اطلاعات کاربر");
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    getProfileHandler();
    getCurrencyHandler();
    fetchDailyLimits();
  }, []);

  const getCurrencyHandler = async () => {
    const result = await getUserCurrency();
    // فقط ارزهایی که موجودی دارند نمایش داده شوند
    const currenciesWithBalance = result.data.filter((currency: CurrencyItem) =>
      parseNumber(currency.balance || "0") > 0
    );
    setUserCurrency(currenciesWithBalance);
  };

  const fetchDailyLimits = async () => {
    try {
      setLimitsLoading(true);
      const result = await getUserLevelDailyLimits();
      if (result.isError) {
        toast.error(result.message || "خطا در دریافت محدودیت‌های روزانه");
      } else {
        setLimitsData(result.data);
      }
    } catch (error) {
      toast.error("خطا در دریافت محدودیت‌های روزانه");
    } finally {
      setLimitsLoading(false);
    }
  };

  const handleWithdrawAll = () => {
    if (filtered && filtered.balance) {
      const balance = parseNumber(filtered.balance);
      setTrxAmount(balance.toString());
      // محاسبه مقدار تومان بر اساس موجودی ارز
      const rate = Number(filtered.toman_sell_price);
      if (rate > 0) {
        const tomanValue = (balance * rate).toFixed(0);
        setTomanAmount(tomanValue);
      }
    }
  };

  useEffect(() => {
    if (id && active) {
      setSelected(Number(id));
    }
  }, [filtered, active]);

  // تبدیل مقدار ارز به تومان
  const handleTrxChange = (value: string) => {
    setTrxAmount(value);
    // اگر مقدار ارز وارد شده و ارزی انتخاب شده باشد
    if (value && filtered) {
      // اطمینان از وجود قیمت ارز
      const rate = Number(filtered.toman_sell_price);
      if (rate > 0) {
        // محاسبه مقدار تومان با ضرب کردن مقدار ارز در قیمت هر واحد
        const tomanValue = (Number(value) * rate).toFixed(0);
        setTomanAmount(tomanValue);
      } else {
        setTomanAmount("");
      }
    } else {
      setTomanAmount("");
    }
  };

  // بررسی کافی بودن موجودی ارز
  const isBalanceSufficient = () => {
    if (!filtered || !filtered.balance || !trxAmount) return true;
    return parseNumber(filtered.balance) >= Number(trxAmount);
  };

  const handleCardSelect = (card: any) => {
    setSelected(card.id);
    setShowCardDropdown(false);
  };

  const sellHandler = async () => {
    if (!filtered || !trxAmount) return;

    // بررسی حداقل مقدار فروش
    const minSellAmount = 0.000001; // حداقل مقدار فروش
    if (Number(trxAmount) < minSellAmount) {
      toast.error(`حداقل مقدار فروش ${minSellAmount} ${filtered.coin_type} است`);
      return;
    }

    // نمایش پیام تأیید
    const tomanReceive = (Number(trxAmount) * Number(filtered.toman_sell_price)).toFixed(0);
    const confirmMessage = `آیا از فروش ${Number(trxAmount).toFixed(6)} ${filtered.coin_type} به مبلغ ${sliceNumber(tomanReceive)} تومان اطمینان دارید؟`;

    if (!confirm(confirmMessage)) {
      return;
    }

    setLoading(true);
    try {
      const result = await sellTrade(trxAmount, filtered?.id);
      if (result.isError) {
        toast.error(result.message);
      } else {
        toast.success(result.message);

        // If we have transaction data, redirect to success page
        if (result.data) {
          const { transaction, wallet_balance, toman_balance, crypto_amount } = result.data;

          // Create URL with transaction data
          const transactionParam = encodeURIComponent(JSON.stringify(transaction));
          const successUrl = `/dashboard/sell/success?transaction=${transactionParam}&wallet_balance=${wallet_balance}&toman_balance=${toman_balance}&crypto_amount=${crypto_amount}&currency_id=${filtered.id}`;

          // Redirect to success page
          router.push(successUrl);
        }
      }
    } catch (error) {
      console.log(error);
      toast.error("خطا در انجام تراکنش");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full max-w-[1200px] mx-auto bg-[#18191D] px-4 py-9 sm:p-5 md:p-6 rounded-2xl">
      {/* Header */}
      <div className="flex flex-col sm:flex-row items-center sm:justify-between gap-3 mb-6">
        <div className="w-full text-right sm:text-right">
          <h1 className="text-xl sm:text-2xl font-medium mb-2">فروش</h1>
          <p className="text-sm text-gray-400">خرید، فروش و مبدل ارز</p>
        </div>
      </div>

      {/* Wallet Balance - موجودی کیف پول */}
      <div className="bg-[#23262F] p-4 rounded-xl mb-4">
        <p className="text-gray-400 mb-1">موجودی تومانی</p>
        <p className="text-xl font-bold">
          {isLoading
            ? "در حال بارگذاری..."
            : `${sliceNumber(Number(info.tomanBalance || 0).toFixed(0))} تومان`}
        </p>
      </div>

      {/* Main Content - Two Columns Layout */}
      <div className="grid md:grid-cols-2 gap-6">
        {/* Right Column - Currency Exchange */}
        <div className="bg-transparent md:bg-[#18191D] p-3 sm:p-4 md:p-6 rounded-2xl">
          {/* نمایش ارز و موجودی */}
          <div className="flex-1 relative">
            {userCurrency.length === 0 ? (
              <div className="bg-yellow-900/30 border border-yellow-600/30 rounded-lg p-4 text-center">
                <p className="text-yellow-400 mb-2">شما هیچ ارزی برای فروش ندارید</p>
                <p className="text-sm text-gray-400">
                  ابتدا ارز خریداری کنید یا ارز به کیف پول خود واریز نمایید
                </p>
              </div>
            ) : (
              <>
                <div
                  className="border border-gray-600 rounded-lg p-2 w-full text-right bg-[#23262F] text-white cursor-pointer flex items-center justify-between"
                  onClick={() => {
                    setShowCardDropdown(!showCardDropdown);
                    setActive(false);
                  }}
                >
                  {filtered ? (
                    <div className="p-2 px-5 text-center w-full cursor-pointer transition-colors flex justify-between items-center gap-x-2">
                      <div className="flex items-center">
                        <Image
                          src={`https://api.exchangim.com/storage/${filtered?.coin_icon}`}
                          height={24}
                          width={24}
                          alt="TRX"
                          className="ml-2"
                        />
                        <p className="text-white">{filtered?.coin_type}</p>
                      </div>
                      <p className="text-[18px]">{parseNumber(filtered?.balance || "0").toFixed(6)} {filtered?.coin_type}</p>
                    </div>
                  ) : (
                    <span>برای انتخاب کلیک کنید</span>
                  )}

                  <svg
                    className={`w-4 h-4 transition-transform duration-300 ${
                      showCardDropdown ? "rotate-180" : ""
                    }`}
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                  >
                    <path d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
                {showCardDropdown && (
                  <ul className="absolute w-full mt-2 bg-[#23262F] border border-gray-600 rounded-xl shadow-xl z-20 max-h-60 overflow-y-auto text-sm text-white">
                    {userCurrency?.map((item) => (
                      <li
                        onClick={() => handleCardSelect(item)}
                        className="p-5 text-center hover:bg-blue-700 cursor-pointer transition-colors flex justify-between items-center gap-x-2"
                        key={item.id}
                      >
                        <div className="flex items-center">
                          <Image
                            src={`https://api.exchangim.com/storage/${item.coin_icon}`}
                            height={24}
                            width={24}
                            alt="TRX"
                            className="ml-2"
                          />
                          <p className="text-white">{item.coin_type}</p>
                        </div>
                        <p className="text-[18px]">{parseNumber(item.balance || "0").toFixed(6)} {item.coin_type}</p>
                      </li>
                    ))}
                  </ul>
                )}
              </>
            )}
          </div>

          <div className="flex flex-col gap-3 mt-4">
            <div className="relative">
              {filtered && (
                <div className="flex items-center border border-gray-600 rounded-lg bg-[#23262F] text-center">
                  <div className="absolute right-3 flex items-center">
                    <Image
                      src={`https://api.exchangim.com/storage/${filtered?.coin_icon}`}
                      height={20}
                      width={20}
                      alt="TRX"
                      className="mr-2"
                    />
                  </div>
                  <input
                    type="text"
                    inputMode="decimal"
                    className="border-0 rounded-lg p-3 pr-12 w-full bg-transparent text-white placeholder-gray-400  text-center outline-white"
                    placeholder={`مقدار ${filtered?.coin_type}`}
                    value={trxAmount}
                    onChange={(e) =>
                      handleTrxChange(e.target.value.replace(/[^0-9.]/g, ""))
                    }
                  />
                </div>
              )}
            </div>

            {/* نمایش قیمت و مبلغ دریافتی */}
            {filtered && trxAmount && Number(trxAmount) > 0 && (
              <div className="bg-[#1C1E24] p-4 rounded-lg border border-gray-600">
                <div className="space-y-3">
                  {/* قیمت هر واحد */}
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">قیمت هر {filtered.coin_type}:</span>
                    <span className="text-white font-medium">
                      {sliceNumber(Number(filtered.toman_buy_price).toFixed(0))} تومان
                    </span>
                  </div>

                  {/* مقدار فروش */}
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">مقدار فروش:</span>
                    <span className="text-white font-medium">
                      {Number(trxAmount).toFixed(6)} {filtered.coin_type}
                    </span>
                  </div>

                  {/* مبلغ دریافتی */}
                  <div className="flex justify-between items-center bg-green-900/30 p-3 rounded-lg border border-green-600/30">
                    <span className="text-green-400 font-medium">مبلغ دریافتی:</span>
                    <span className="text-green-300 font-bold text-lg">
                      {sliceNumber((Number(trxAmount) * Number(filtered.toman_sell_price)).toFixed(0))} تومان
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="flex flex-col justify-between gap-3 mt-4">
            {/* نمایش خطای کمبود موجودی */}
            {!isBalanceSufficient() && trxAmount && filtered && (
              <div className="bg-red-900/50 text-red-300 p-3 rounded-lg mt-2">
                <p>موجودی {filtered.coin_type} شما برای فروش کافی نمیباشد.</p>
                <div className="flex justify-between items-center mt-2">
                  <p>
                    موجودی فعلی: {parseNumber(filtered.balance || "0").toFixed(6)} {filtered.coin_type}
                  </p>
                  <p>
                    مقدار درخواستی: {Number(trxAmount).toFixed(6)} {filtered.coin_type}
                  </p>
                </div>
              </div>
            )}

            <div className="flex justify-between items-center mt-2">
              <p className="text-sm text-gray-400">
                موجودی {filtered?.coin_type || 'ارز'}:
                {isLoading
                  ? "در حال بارگذاری..."
                  : filtered
                  ? `${parseNumber(filtered.balance || "0").toFixed(6)} ${filtered.coin_type}`
                  : "ارزی انتخاب نشده"}
              </p>
            </div>
            <div className="flex flex-col gap-2">
              <p className="text-sm text-gray-400">انتخاب سریع:</p>
              <div className="flex gap-2 flex-wrap">
                <button
                  onClick={() => {
                    if (filtered && filtered.balance) {
                      const balance = parseNumber(filtered.balance);
                      const amount = (balance * 0.25).toFixed(6);
                      setTrxAmount(amount);
                      const tomanValue = (Number(amount) * Number(filtered.toman_sell_price)).toFixed(0);
                      setTomanAmount(tomanValue);
                    }
                  }}
                  className="text-xs text-[#4899EB] hover:text-[#5BA7F7] transition-colors bg-[#1A1D21] px-3 py-1 rounded border border-[#353945]/50"
                >
                  25%
                </button>
                <button
                  onClick={() => {
                    if (filtered && filtered.balance) {
                      const balance = parseNumber(filtered.balance);
                      const amount = (balance * 0.5).toFixed(6);
                      setTrxAmount(amount);
                      const tomanValue = (Number(amount) * Number(filtered.toman_sell_price)).toFixed(0);
                      setTomanAmount(tomanValue);
                    }
                  }}
                  className="text-xs text-[#4899EB] hover:text-[#5BA7F7] transition-colors bg-[#1A1D21] px-3 py-1 rounded border border-[#353945]/50"
                >
                  50%
                </button>
                <button
                  onClick={() => {
                    if (filtered && filtered.balance) {
                      const balance = parseNumber(filtered.balance);
                      const amount = (balance * 0.75).toFixed(6);
                      setTrxAmount(amount);
                      const tomanValue = (Number(amount) * Number(filtered.toman_sell_price)).toFixed(0);
                      setTomanAmount(tomanValue);
                    }
                  }}
                  className="text-xs text-[#4899EB] hover:text-[#5BA7F7] transition-colors bg-[#1A1D21] px-3 py-1 rounded border border-[#353945]/50"
                >
                  75%
                </button>
                <button
                  onClick={handleWithdrawAll}
                  className="text-xs text-[#4899EB] hover:text-[#5BA7F7] transition-colors bg-[#1A1D21] px-3 py-1 rounded border border-[#353945]/50"
                >
                  100%
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-gradient-to-br from-[#23262F]/90 to-[#1C1E24] p-5 rounded-xl border border-orange-500/20 shadow-md relative overflow-hidden"
          >
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-orange-600/20 via-orange-400/40 to-orange-600/20"></div>
            <div className="absolute -top-10 -right-10 w-24 h-24 bg-orange-500/10 rounded-full blur-2xl"></div>

            <div className="flex items-center mb-3">
              <div className="bg-orange-500/20 p-2 rounded-full mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-orange-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <p className="text-orange-400 font-medium text-lg">محدودیت های فروش</p>
            </div>

            {limitsLoading ? (
              <div className="bg-orange-500/5 p-3 rounded-lg border border-orange-500/10 flex justify-center">
                <motion.div
                  animate={{ opacity: [0.5, 1, 0.5] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                  className="text-gray-400"
                >
                  در حال بارگذاری...
                </motion.div>
              </div>
            ) : limitsData ? (
              <div className="bg-orange-500/5 p-3 rounded-lg border border-orange-500/10">
                <div className="space-y-3">
                  {/* Current Usage */}
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-300">استفاده شده امروز:</span>
                    <span className="text-white font-medium">{limitsData.sell_limits.formatted_used_today}</span>
                  </div>
                  
                  {/* Daily Limit */}
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-300">محدودیت روزانه:</span>
                    <span className="text-white font-medium">{limitsData.sell_limits.formatted_daily_limit}</span>
                  </div>

                  {/* Progress Bar */}
                  <div className="w-full bg-gray-700/50 rounded-full h-3 mt-3 relative overflow-hidden">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: `${Math.min(100, Math.round((limitsData.sell_limits.used_today / limitsData.sell_limits.daily_limit) * 100))}%` }}
                      transition={{ duration: 1, ease: "easeOut" }}
                      className={`h-3 rounded-full ${
                        limitsData.sell_limits.limit_reached 
                          ? 'bg-gradient-to-r from-red-500 to-red-400' 
                          : 'bg-gradient-to-r from-orange-500 to-orange-300'
                      }`}
                    />
                    {limitsData.sell_limits.used_today > 0 && (
                      <motion.div
                        className="absolute top-0 left-0 right-0 h-full w-20 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                        animate={{ x: ['-100%', '400%'] }}
                        transition={{ duration: 2, repeat: Infinity, repeatDelay: 1 }}
                      />
                    )}
                  </div>
                  
                  {/* Remaining */}
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-400">باقیمانده:</span>
                    <span className={`text-sm font-medium ${
                      limitsData.sell_limits.limit_reached ? 'text-red-400' : 'text-green-400'
                    }`}>
                      {limitsData.sell_limits.formatted_remaining_today}
                    </span>
                  </div>

                  {/* Status */}
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-400">وضعیت:</span>
                    <span className={`text-sm font-medium ${
                      limitsData.sell_limits.limit_reached ? 'text-red-400' : 'text-green-400'
                    }`}>
                      {limitsData.sell_limits.limit_reached ? 'محدودیت تکمیل شده' : 'قابل فروش'}
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-orange-500/5 p-3 rounded-lg border border-orange-500/10 text-center">
                <p className="text-sm text-gray-400">خطا در دریافت اطلاعات محدودیت‌ها</p>
              </div>
            )}
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="bg-gradient-to-br from-[#23262F]/90 to-[#1C1E24] p-5 rounded-xl border border-green-500/20 shadow-md relative overflow-hidden"
          >
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-600/20 via-green-400/40 to-green-600/20"></div>
            <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-green-500/10 rounded-full blur-2xl"></div>

            <div className="flex items-center mb-3">
              <div className="bg-green-500/20 p-2 rounded-full mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <p className="text-green-400 font-medium text-lg">نکات مهم</p>
            </div>

            <div className="space-y-3">
              <div className="bg-green-500/5 p-3 rounded-lg border border-green-500/10">
                <p className="text-sm text-gray-300">
                  فقط ارزهایی که در کیف پول شما موجودی دارند قابل فروش هستند.
                </p>
              </div>

              <div className="bg-green-500/5 p-3 rounded-lg border border-green-500/10">
                <p className="text-sm text-gray-300">
                  قیمت‌ها بر اساس نرخ لحظه‌ای بازار محاسبه می‌شوند.
                </p>
              </div>

              <div className="bg-green-500/5 p-3 rounded-lg border border-green-500/10">
                <p className="text-sm text-gray-300">
                  پیش از ثبت درخواست فروش از میزان ارز وارد شده و قیمت آن اطمینان حاصل فرمایید.
                </p>
              </div>

              <div className="bg-green-500/5 p-3 rounded-lg border border-green-500/10">
                <p className="text-sm text-gray-300">
                  مبلغ تومانی به کیف پول تومانی شما واریز خواهد شد.
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Submit Button */}
      {userCurrency.length > 0 && (
        <div className="text-center mt-6">
          <BtnLoader
            label="درخواست فروش"
            pending={loading}
            className={`px-8 py-3 rounded-xl ${
              trxAmount && isBalanceSufficient() && filtered
                ? "bg-green-600 hover:bg-green-300 transition cursor-pointer"
                : "bg-gray-600 cursor-not-allowed opacity-70"
            } sm:text-base text-sm`}
            onClick={
              trxAmount && isBalanceSufficient() && filtered ? sellHandler : undefined
            }
          />
        </div>
      )}
    </div>
  );
};

export default Sell;
