"use client";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { getReferralLink, getReferralList } from "@/requests/dashboardRequest";
import { FaInstagram, FaTelegramPlane, <PERSON>a<PERSON>wi<PERSON>, FaChartBar, FaUsers, FaBullseye, FaMoneyBillWave, FaCrown, FaUserCircle, FaLink, FaCopy, FaShareAlt } from "react-icons/fa";
import { QRCodeCanvas } from "qrcode.react";

export default function ReferralPage() {
  const [referralCode, setReferralCode] = useState<string>("");
  const [referralLink, setReferralLink] = useState<string>("");
  const [referrals, setReferrals] = useState<any[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  
  // Advanced stats
  const [stats, setStats] = useState({
    totalReferrals: 0,
    activeReferrals: 0,
    totalEarnings: 0,
    thisMonthEarnings: 0,
    referralLevel: "برنزی",
    nextLevel: "نقره‌ای",
    progressToNextLevel: 0,
    referralBonus: "5%",
    maxReferrals: 10,
    referralHistory: [],
    topReferrers: [],
    achievements: [],
    referralRules: [],
    commissionRates: [],
    withdrawalHistory: []
  });
  
  // UI States
  const [activeTab, setActiveTab] = useState('overview');
  const [showShareModal, setShowShareModal] = useState(false);
  const [showStatsModal, setShowStatsModal] = useState(false);
  const [copied, setCopied] = useState(false);
  const [animateStats, setAnimateStats] = useState(false);

  // Social Campaigns
  const [campaigns] = useState([
    {
      id: 'instagram',
      name: 'اینستاگرام',
      icon: 'instagram',
      color: 'from-pink-500 to-purple-600',
      bgColor: 'bg-pink-500/10',
      borderColor: 'border-pink-500/30',
      tasks: [
        { id: 'follow', title: 'صفحه اکسچنجیم را فالو کنید', reward: '1000 تومان', completed: false },
        { id: 'like', title: '3 پست آخر را لایک کنید', reward: '500 تومان', completed: false },
        { id: 'comment', title: 'در پست آخر کامنت بگذارید', reward: '1500 تومان', completed: false },
        { id: 'share', title: 'پست را در استوری به اشتراک بگذارید', reward: '2000 تومان', completed: false }
      ]
    },
    {
      id: 'telegram',
      name: 'تلگرام',
      icon: 'telegram',
      color: 'from-blue-500 to-cyan-600',
      bgColor: 'bg-blue-500/10',
      borderColor: 'border-blue-500/30',
      tasks: [
        { id: 'join', title: 'در کانال اکسچنجیم عضو شوید', reward: '1000 تومان', completed: false },
        { id: 'share', title: 'پست کانال را فوروارد کنید', reward: '800 تومان', completed: false },
        { id: 'invite', title: '3 نفر را به کانال دعوت کنید', reward: '1500 تومان', completed: false }
      ]
    },
    {
      id: 'twitter',
      name: 'توییتر',
      icon: 'twitter',
      color: 'from-sky-500 to-blue-600',
      bgColor: 'bg-sky-500/10',
      borderColor: 'border-sky-500/30',
      tasks: [
        { id: 'follow', title: 'اکانت اکسچنجیم را فالو کنید', reward: '1000 تومان', completed: false },
        { id: 'retweet', title: 'آخرین توییت را ریتوییت کنید', reward: '1200 تومان', completed: false },
        { id: 'like', title: '3 توییت آخر را لایک کنید', reward: '600 تومان', completed: false }
      ]
    }
  ]);

  // Helper for campaign icon
  const getCampaignIcon = (icon: string) => {
    switch (icon) {
      case 'instagram':
        return <FaInstagram className="w-7 h-7" />;
      case 'telegram':
        return <FaTelegramPlane className="w-7 h-7" />;
      case 'twitter':
        return <FaTwitter className="w-7 h-7" />;
      default:
        return null;
    }
  };

  // Helper for tab icon
  const getTabIcon = (id: string) => {
    switch (id) {
      case 'overview':
        return <FaChartBar className="w-4 h-4" />;
      case 'referrals':
        return <FaUsers className="w-4 h-4" />;
      case 'campaigns':
        return <FaBullseye className="w-4 h-4" />;
      case 'earnings':
        return <FaMoneyBillWave className="w-4 h-4" />;
      default:
        return null;
    }
  };

  useEffect(() => {
    async function fetchReferralData() {
      setLoading(true);
      setError("");
      try {
        const [linkResult, listResult] = await Promise.all([
          getReferralLink(),
          getReferralList()
        ]);
        
        if (!linkResult.isError && linkResult.data) {
          setReferralCode(linkResult.data.referral_code);
          setReferralLink(linkResult.data.referral_link);
        } else {
          setError(linkResult.message || "خطا در دریافت کد دعوت");
        }
        
        if (!listResult.isError && listResult.data) {
          setReferrals(listResult.data.referrals);
          const totalReward = listResult.data.referrals.reduce((sum: number, referral: any) => {
            return sum + parseFloat(referral.total_reward || 0);
          }, 0);
          setTotal(totalReward);
          
          setStats(prev => ({
            ...prev,
            totalReferrals: listResult.data.referrals.length,
            activeReferrals: listResult.data.referrals.length,
            totalEarnings: totalReward,
            thisMonthEarnings: totalReward * 0.3,
          }));
        } else {
          setError(listResult.message || "خطا در دریافت لیست دعوت‌ها");
        }
      } catch (e) {
        console.error("Referral API Error:", e);
        setError("خطا در ارتباط با سرور");
      }
      setLoading(false);
    }
    fetchReferralData();
  }, []);

  const handleCopy = (text: string, type: 'code' | 'link') => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    toast.success(type === 'code' ? "کد دعوت کپی شد!" : "لینک دعوت کپی شد!");
    setTimeout(() => setCopied(false), 2000);
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'دعوت به اکسچنجیم',
          text: 'با کد دعوت من در اکسچنجیم ثبت‌نام کنید و پاداش دریافت کنید!',
          url: referralLink
        });
      } catch (error) {
        setShowShareModal(true);
      }
    } else {
      setShowShareModal(true);
    }
  };

  const shareToSocialMedia = (platform: string) => {
    const text = `با کد دعوت من در اکسچنجیم ثبت‌نام کنید: ${referralCode}`;
    const url = referralLink;
    
    const shareUrls = {
      telegram: `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`,
      whatsapp: `https://wa.me/?text=${encodeURIComponent(text + ' ' + url)}`,
      twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`,
      instagram: `https://www.instagram.com/`
    };
    
    window.open(shareUrls[platform as keyof typeof shareUrls], '_blank');
  };

  const handleCampaignTask = (campaignId: string, taskId: string) => {
    toast.success(`درخواست شما برای ${campaignId} ثبت شد!`);
    // Here you would typically make an API call to verify the task
  };

  // Leaderboard mock data
  const leaderboard = [
    { name: "علی رضایی", reward: 120000, avatar: null },
    { name: "مریم محمدی", reward: 95000, avatar: null },
    { name: "سینا احمدی", reward: 87000, avatar: null },
    { name: "شما", reward: total, avatar: null, isMe: true },
  ];

  // Add a mock for total distributed rewards
  const totalDistributed = 12500000; // تومان (mocked)

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          <span className="text-gray-400">در حال بارگذاری...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-500/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <p className="text-red-500">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8 p-4 md:p-8 relative z-10">
      {/* Header */}
      <div className="rounded-2xl bg-[#23262F] border border-gray-700/30 shadow mb-8 p-6 flex flex-col md:flex-row items-center justify-between gap-6">
        <div className="flex-1 text-center md:text-right">
          <h1 className="text-2xl md:text-3xl font-bold text-white mb-2 flex items-center gap-2 justify-center md:justify-start">
            سیستم دعوت اکسچنجیم
          </h1>
          <p className="text-base text-gray-400 mb-4">با دعوت دوستان خود، درآمد و امتیاز ویژه کسب کنید!</p>
          <div className="flex flex-col md:flex-row items-center gap-4">
            <div className="flex items-center gap-2 bg-[#18191D] rounded-xl px-4 py-2 border border-gray-700/30">
              <FaLink className="text-blue-400" />
              <span className="font-mono text-white select-all">{referralLink}</span>
              <button onClick={() => handleCopy(referralLink, 'link')} className="ml-2 text-gray-400 hover:text-blue-400 transition"><FaCopy /></button>
              <button onClick={handleShare} className="ml-2 text-gray-400 hover:text-blue-400 transition"><FaShareAlt /></button>
            </div>
            <div className="bg-[#18191D] rounded-xl p-2 flex items-center gap-2 border border-gray-700/30">
              <QRCodeCanvas value={referralLink} size={56} bgColor="#23262F" fgColor="#fff" />
              <span className="text-xs text-gray-400">QR کد دعوت</span>
            </div>
          </div>
        </div>
        <div className="flex-1 flex flex-col items-center gap-4">
          <div className="bg-[#18191D] rounded-2xl p-6 flex flex-col items-center border border-gray-700/30">
            <span className="text-gray-400 mb-2">کد دعوت شما</span>
            <span className="font-mono text-2xl text-blue-400 mb-2">{referralCode}</span>
            <button onClick={() => handleCopy(referralCode, 'code')} className="bg-gradient-to-r from-blue-600 to-blue-500 text-white px-4 py-2 rounded-lg font-bold shadow hover:scale-105 transition">کپی کد</button>
          </div>
        </div>
      </div>

      {/* Stats & Motivational Card */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 justify-center items-center">
        <div className="bg-[#18191D] rounded-xl p-6 border border-gray-700/30 flex flex-col items-center">
          <FaUsers className="text-blue-400 text-2xl mb-2" />
          <span className="text-gray-400">کل دعوت‌ها</span>
          <span className="text-xl font-bold text-white">{stats.totalReferrals}</span>
        </div>
        <div className="bg-[#18191D] rounded-xl p-6 border border-gray-700/30 flex flex-col items-center">
          <FaMoneyBillWave className="text-green-400 text-2xl mb-2" />
          <span className="text-gray-400">درآمد کل</span>
          <span className="text-xl font-bold text-white">{Number(total).toLocaleString('en-US', { maximumFractionDigits: 0 })} تومان</span>
        </div>
        <div className="bg-[#18191D] rounded-xl p-6 border border-gray-700/30 flex flex-col items-center">
          <FaChartBar className="text-blue-400 text-2xl mb-2" />
          <span className="text-gray-400">درآمد این ماه</span>
          <span className="text-xl font-bold text-white">{Number(stats.thisMonthEarnings).toLocaleString('en-US', { maximumFractionDigits: 0 })} تومان</span>
        </div>
      </div>
      {/* Motivational Card */}
      <div className="my-8 bg-[#23262F] rounded-xl p-6 border border-gray-700/30 flex items-center gap-4">
        <div className="flex items-center justify-center w-14 h-14 rounded-full bg-yellow-500/10 border border-yellow-400/20">
          <FaCrown className="text-yellow-400 text-3xl" />
        </div>
        <div>
          <div className="text-lg font-bold text-white mb-1">تا الان بیش از {Number(totalDistributed).toLocaleString('en-US', { maximumFractionDigits: 0 })} تومان پاداش دعوت به کاربران پرداخت شده!</div>
          <div className="text-gray-400 text-sm">شما هم می‌توانید با دعوت دوستان، سهم بیشتری از این پاداش‌ها داشته باشید.</div>
        </div>
      </div>

      {/* Leaderboard */}
      <div className="bg-[#23262F] rounded-xl p-6 border border-gray-700/30 mb-8">
        <div className="flex items-center gap-2 mb-4">
          <FaCrown className="text-yellow-400 text-xl" />
          <span className="text-lg font-bold text-white">برترین دعوت‌کنندگان</span>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full text-white">
            <thead>
              <tr className="text-blue-400 text-sm">
                <th className="py-2 px-4 text-right">ردیف</th>
                <th className="py-2 px-4 text-right">نام</th>
                <th className="py-2 px-4 text-right">درآمد (تومان)</th>
              </tr>
            </thead>
            <tbody>
              {leaderboard.map((item, idx) => (
                <tr key={idx} className={item.isMe ? "bg-blue-600/10" : "hover:bg-[#18191D]/80 transition"}>
                  <td className="py-2 px-4">{idx + 1}</td>
                  <td className="py-2 px-4 flex items-center gap-2">
                    <FaUserCircle className="text-xl text-gray-400" />
                    {item.name}
                    {item.isMe && <span className="ml-2 px-2 py-0.5 bg-green-500/20 text-green-200 rounded text-xs">شما</span>}
                  </td>
                  <td className="py-2 px-4">{Number(item.reward).toLocaleString('en-US', { maximumFractionDigits: 0 })}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-2 border-b border-gray-700/30 mb-4">
        {[
          { id: 'overview', label: 'نمای کلی' },
          { id: 'referrals', label: 'دعوت‌ها' },
          { id: 'campaigns', label: 'کمپین‌ها' },
          { id: 'earnings', label: 'درآمدها' }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex items-center space-x-2 px-4 py-3 rounded-t-lg font-medium transition-all duration-200 ${
              activeTab === tab.id
                ? 'bg-[#23262F] text-white border-b-2 border-blue-600 shadow'
                : 'text-gray-400 hover:text-white hover:bg-[#23262F]/70'
            }`}
          >
            {getTabIcon(tab.id)}
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="bg-[#18191D] rounded-xl border border-gray-700/30">
        {activeTab === 'overview' && (
          <div className="p-8 text-center text-gray-300">
            <h2 className="text-xl font-bold mb-2 text-white">به سیستم دعوت اکسچنجیم خوش آمدید!</h2>
            <p className="mb-4">با اشتراک‌گذاری کد یا لینک دعوت خود، دوستانتان را به اکسچنجیم دعوت کنید و از مزایای ویژه بهره‌مند شوید.</p>
            <div className="flex flex-col md:flex-row gap-6 justify-center items-center mt-8">
              <div className="flex-1 bg-[#23262F] rounded-xl p-6 border border-gray-700/30">
                <h3 className="font-bold text-base mb-2 text-white">راهنمای دعوت</h3>
                <ul className="text-right text-gray-400 space-y-2 text-sm">
                  <li>۱. لینک یا کد دعوت خود را برای دوستانتان ارسال کنید.</li>
                  <li>۲. هر دوست با ثبت‌نام و انجام اولین تراکنش، برای شما پاداش ثبت می‌کند.</li>
                  <li>۳. هرچه بیشتر دعوت کنید، سطح و درآمد شما بالاتر می‌رود.</li>
                </ul>
              </div>
              <div className="flex-1 bg-[#23262F] rounded-xl p-6 border border-gray-700/30">
                <h3 className="font-bold text-base mb-2 text-white">قوانین و مزایا</h3>
                <ul className="text-right text-gray-400 space-y-2 text-sm">
                  <li>• هر کاربر فقط یکبار می‌تواند دعوت شود.</li>
                  <li>• پاداش‌ها پس از تایید تراکنش واریز می‌شوند.</li>
                  <li>• دعوت بیشتر = سطح بالاتر و درصد پاداش بیشتر!</li>
                </ul>
              </div>
            </div>
          </div>
        )}
        {activeTab === 'referrals' && (
          <div className="p-8">
            <h2 className="text-lg font-bold text-white mb-4">لیست دعوت‌های شما</h2>
            <div className="overflow-x-auto rounded-xl">
              <table className="min-w-full text-gray-200">
                <thead>
                  <tr className="text-blue-400 text-sm">
                    <th className="py-2 px-4 text-right">نام</th>
                    <th className="py-2 px-4 text-right">شماره</th>
                    <th className="py-2 px-4 text-right">درآمد</th>
                    <th className="py-2 px-4 text-right">تراکنش</th>
                    <th className="py-2 px-4 text-right">تاریخ عضویت</th>
                  </tr>
                </thead>
                <tbody>
                  {referrals.length === 0 ? (
                    <tr>
                      <td colSpan={5} className="text-center py-8 text-gray-500">هنوز دعوتی ثبت نشده است.</td>
                    </tr>
                  ) : (
                    referrals.map((referral, idx) => (
                      <tr key={idx} className="hover:bg-[#23262F]/80 transition">
                        <td className="py-2 px-4">{referral.user.firstname} {referral.user.lastname}</td>
                        <td className="py-2 px-4">{referral.user.phone}</td>
                        <td className="py-2 px-4">{Number(referral.total_reward).toLocaleString('en-US', { maximumFractionDigits: 0 })} تومان</td>
                        <td className="py-2 px-4">{referral.transactions_count}</td>
                        <td className="py-2 px-4">{new Date(referral.joined_at).toLocaleDateString('fa-IR')}</td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}
        {activeTab === 'campaigns' && (
          <div className="p-8">
            <h2 className="text-lg font-bold text-white mb-4">کمپین‌های اجتماعی</h2>
            <div className="grid gap-6 md:grid-cols-2">
              {campaigns.map((campaign) => (
                <div key={campaign.id} className={`bg-[#23262F] rounded-xl border ${campaign.borderColor} p-6 flex flex-col gap-4`}>
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`w-12 h-12 bg-gradient-to-r ${campaign.color} rounded-full flex items-center justify-center text-white text-2xl`}>{getCampaignIcon(campaign.icon)}</div>
                    <div>
                      <h3 className="text-base font-bold text-white">{campaign.name}</h3>
                      <p className="text-gray-400 text-xs">کمپین {campaign.name}</p>
                    </div>
                  </div>
                  <div className="space-y-3">
                    {campaign.tasks.map((task) => (
                      <div key={task.id} className="flex items-center justify-between p-3 bg-[#18191D] rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${task.completed ? 'bg-green-500/20' : 'bg-gray-500/20'}`}>{task.completed ? <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" /></svg> : <div className="w-2 h-2 bg-gray-400 rounded-full"></div>}</div>
                          <div>
                            <p className="text-white font-medium text-sm">{task.title}</p>
                            <p className="text-gray-400 text-xs">پاداش: {task.reward}</p>
                          </div>
                        </div>
                        <button onClick={() => handleCampaignTask(campaign.id, task.id)} disabled={task.completed} className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${task.completed ? 'bg-green-600 text-white cursor-not-allowed' : `bg-gradient-to-r from-blue-600 to-blue-500 text-white hover:opacity-80`}`}>{task.completed ? 'تکمیل شده' : 'انجام دهید'}</button>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        {activeTab === 'earnings' && (
          <div className="p-8">
            <h2 className="text-lg font-bold text-white mb-4">تاریخچه درآمدها</h2>
            <div className="overflow-x-auto rounded-xl">
              <table className="min-w-full text-gray-200">
                <thead>
                  <tr className="text-blue-400 text-sm">
                    <th className="py-2 px-4 text-right">تاریخ</th>
                    <th className="py-2 px-4 text-right">مبلغ</th>
                    <th className="py-2 px-4 text-right">توضیحات</th>
                  </tr>
                </thead>
                <tbody>
                  {/* Mocked data for now */}
                  <tr>
                    <td className="py-2 px-4">1403/03/01</td>
                    <td className="py-2 px-4">10,000 تومان</td>
                    <td className="py-2 px-4">پاداش دعوت کاربر جدید</td>
                  </tr>
                  <tr>
                    <td className="py-2 px-4">1403/02/28</td>
                    <td className="py-2 px-4">5,000 تومان</td>
                    <td className="py-2 px-4">پاداش تراکنش دعوتی</td>
                  </tr>
                  <tr>
                    <td className="py-2 px-4">1403/02/25</td>
                    <td className="py-2 px-4">15,000 تومان</td>
                    <td className="py-2 px-4">پاداش ارتقا سطح</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 