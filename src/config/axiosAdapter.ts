import axios from "axios";
import { cookies } from "next/headers";
import { hybridEncrypt, shouldEncryptData } from "@/utils/rsaEncrypt";
import { getClientIP, getCachedIP } from "@/utils/getClientIP";
// {60z~QrW~WtUm<@
const getToken = async () => {
  const cookieStore = await cookies();
  const token = cookieStore.get("token");
  return token?.value;
};

const createAxiosInstance = async () => {
  const token = await getToken();

  const axiosInstance = axios.create({
    baseURL: process.env.API_BASE_URL,
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
      Authorization: token ? `Bearer ${token}` : "",
    },
  });

  // Request Interceptor for encryption and IP injection
  axiosInstance.interceptors.request.use(
    async (config) => {
      // Get client IP address
      let clientIP = getCachedIP(); // Try to get cached IP first
      if (!clientIP) {
        try {
          clientIP = await getClientIP(); // Fetch IP if not cached
        } catch (error) {
          console.warn('Failed to get client IP:', error);
          clientIP = 'unknown';
        }
      }

      // Add IP to headers
      config.headers['X-Client-IP'] = clientIP;
      config.headers['X-Real-IP'] = clientIP;
      config.headers['X-Forwarded-For'] = clientIP;

      // Only encrypt data for POST, PUT, PATCH requests
      if (["post", "put", "patch"].includes(config.method?.toLowerCase() || "")) {
        if (shouldEncryptData(config.data)) {
          try {
            const dataWithIP = {
              ...config.data,
              client_ip: clientIP,
              user_ip: clientIP
            };
            const encryptedData = hybridEncrypt(dataWithIP);
            config.data = encryptedData;
          } catch (error) {
            console.error('Failed to encrypt request data:', error);
            if (config.data && typeof config.data === 'object') {
              config.data.client_ip = clientIP;
              config.data.user_ip = clientIP;
            }
          }
        } else {
          if (config.data && typeof config.data === 'object' && !(config.data instanceof FormData)) {
            config.data.client_ip = clientIP;
            config.data.user_ip = clientIP;
          }
        }
      }

      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Response Interceptor
  axiosInstance.interceptors.response.use(
    (response) => {
      return response;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  return axiosInstance;
};

export default createAxiosInstance;
