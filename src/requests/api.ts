// src/requests/api.ts

const API_BASE_URL = process.env.API_BASE_URL || 'https://api.exchangim.com';

export async function apiFetch<T = any>(endpoint: string, options?: RequestInit): Promise<T> {
  const url = endpoint.startsWith('http') ? endpoint : `${API_BASE_URL}${endpoint}`;
  const res = await fetch(url, options);
  if (!res.ok) {
    throw new Error(`API error: ${res.status} ${res.statusText}`);
  }
  return res.json();
} 