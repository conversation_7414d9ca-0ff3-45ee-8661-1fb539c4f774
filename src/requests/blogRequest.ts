"use server";

// Blog API functions for fetching blog posts and categories

// Types for blog data
export interface BlogPost {
  id: number;
  title: string;
  excerpt: string;
  body: string;
  cover: string;
  status: string;
  user_id: number;
  category_id: number;
  published_at: string;
  created_at: string;
  updated_at: string;
  category: {
    id: number;
    name: string;
    slug: string;
    created_at: string;
    updated_at: string;
  };
  user: {
    id: number;
    email: string;
    firstname: string;
    lastname: string;
    [key: string]: any;
  };
  tags: Array<{
    id: number;
    name: string;
    slug: string;
    created_at: string;
    updated_at: string;
    pivot: {
      blog_post_id: number;
      blog_tag_id: number;
    };
  }>;
}

export interface BlogCategory {
  id: number;
  name: string;
  slug: string;
  created_at: string;
  updated_at: string;
}

export interface BlogPostsResponse {
  status: string;
  result: {
    current_page: number;
    data: BlogPost[];
    first_page_url: string;
    from: number;
    last_page: number;
    last_page_url: string;
    links: Array<{
      url: string | null;
      label: string;
      active: boolean;
    }>;
    next_page_url: string | null;
    path: string;
    per_page: number;
    prev_page_url: string | null;
    to: number;
    total: number;
  };
}

export interface BlogCategoriesResponse {
  status: string;
  result: BlogCategory[];
}

// Get blog posts with pagination
export async function getBlogPosts(page: number = 1, category?: string): Promise<{
  isError: boolean;
  data?: BlogPostsResponse['result'];
  message?: string;
}> {
  try {
    const baseUrl = process.env.API_BASE_URL || 'https://api.exchangim.com/api/';
    let url = `${baseUrl}blog/posts?page=${page}`;

    if (category) {
      url += `&category=${category}`;
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      cache: 'no-store', // Always fetch fresh data
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: BlogPostsResponse = await response.json();

    return {
      isError: false,
      data: data.result,
    };
  } catch (error) {
    console.error('Error fetching blog posts:', error);
    return {
      isError: true,
      message: 'خطا در دریافت مقالات بلاگ',
    };
  }
}

// Get blog categories
export async function getBlogCategories(): Promise<{
  isError: boolean;
  data?: BlogCategory[];
  message?: string;
}> {
  try {
    const baseUrl = process.env.API_BASE_URL || 'https://api.exchangim.com/api/';
    const url = `${baseUrl}blog/categories`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      cache: 'force-cache', // Categories don't change often
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: BlogCategoriesResponse = await response.json();

    return {
      isError: false,
      data: data.result,
    };
  } catch (error) {
    console.error('Error fetching blog categories:', error);
    return {
      isError: true,
      message: 'خطا در دریافت دسته‌بندی‌های بلاگ',
    };
  }
}

// Get single blog post by ID
export async function getBlogPost(id: number): Promise<{
  isError: boolean;
  data?: BlogPost;
  message?: string;
}> {
  try {
    const baseUrl = process.env.API_BASE_URL || 'https://api.exchangim.com/api/';
    const url = `${baseUrl}blog/posts/${id}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      cache: 'no-store', // Always fetch fresh data for individual posts
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    return {
      isError: false,
      data: data.result,
    };
  } catch (error) {
    console.error('Error fetching blog post:', error);
    return {
      isError: true,
      message: 'خطا در دریافت مقاله بلاگ',
    };
  }
}


