// Helper functions for blog functionality

// Helper function to format Persian date
export function formatPersianDate(dateString: string): string {
  try {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fa-IR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  } catch (error) {
    return dateString;
  }
}

// Helper function to get full image URL
export function getBlogImageUrl(imagePath: string): string {
  if (imagePath.startsWith('http')) {
    return imagePath;
  }
  const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api.exchangim.com';
  return `${baseUrl}/storage/${imagePath}`;
}

// Helper function to truncate text
export function truncateText(text: string, maxLength: number = 150): string {
  if (text.length <= maxLength) {
    return text;
  }
  return text.substring(0, maxLength).trim() + '...';
}

// Helper function to format relative time
export function formatRelativeTime(dateString: string): string {
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) {
      return 'امروز';
    } else if (diffInDays === 1) {
      return 'دیروز';
    } else if (diffInDays < 7) {
      return `${diffInDays} روز پیش`;
    } else if (diffInDays < 30) {
      const weeks = Math.floor(diffInDays / 7);
      return `${weeks} هفته پیش`;
    } else if (diffInDays < 365) {
      const months = Math.floor(diffInDays / 30);
      return `${months} ماه پیش`;
    } else {
      const years = Math.floor(diffInDays / 365);
      return `${years} سال پیش`;
    }
  } catch (error) {
    return dateString;
  }
}
